'use client';
import { useUpdateInvoiceMutation } from '@/api/invoices/update-invoice';
import {
  useDeleteInvoiceMutation,
  useGetInvoiceByIdQuery,
  useLinkInvoiceWithTransactionMutation,
} from '@/api/newsf/queries';
import { useGetClientTransactionsQuery } from '@/api/transactions/get-client-transaction';
import { toaster } from '@/components/ui/toaster';
import { queryKey } from '@/constants/query-key';
import { determineInvoiceStatus } from '@/reuseables/invoice/helpers';
import { useDisclosure } from '@chakra-ui/react';
import { useQueryClient } from '@tanstack/react-query';
import { useParams, useRouter } from 'next/navigation';
import { useState } from 'react';

export const useLinkInvoiceItemHook = ({ invoice_id }: any = {}) => {
  const params = useParams();

  const [selectedTransactions, setSelectedTransactions] = useState<number[]>(
    []
  );
  const [loading, setLoading] = useState(false);

  const itemDisclosure = useDisclosure();

  const { data: invoiceData, isFetching } = useGetInvoiceByIdQuery(
    Number(params?.invoice_id || invoice_id),
    {
      enabled: !!params?.invoice_id || !!invoice_id,
    }
  );
  const { data: clientTransactions } = useGetClientTransactionsQuery(
    invoiceData?.data?.client_id,
    {
      enabled: !!invoiceData?.data?.client_id,
    }
  );
  const { mutateAsync: updateInvoices } = useUpdateInvoiceMutation();

  const router = useRouter();
  const queryClient = useQueryClient();
  const { mutateAsync: linkInvoiceWithTransaction } =
    useLinkInvoiceWithTransactionMutation();
  const { mutateAsync: deleteInvoice, isLoading: deleteLoading } =
    useDeleteInvoiceMutation();
  // queryKey: ['newsf-get-invoice-by-id', id],

  const handleTransactionSelect = (
    transactionId: number,
    isChecked: boolean
  ) => {
    if (isChecked) {
      setSelectedTransactions((prev) => [...prev, transactionId]);
    } else {
      setSelectedTransactions((prev) =>
        prev.filter((id) => id !== transactionId)
      );
    }
  };

  const handleSelectAll = (checked: boolean, transactions: any[]) => {
    if (checked) {
      setSelectedTransactions(transactions.map((t) => t.id));
    } else {
      setSelectedTransactions([]);
    }
  };

  const formatCurrency = (amount: number, currencyCode: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currencyCode || 'USD',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getPaymentMethodDisplay = (method: string) => {
    return (
      method
        ?.replace(/_/g, ' ')
        .toLowerCase()
        .replace(/\b\w/g, (l) => l.toUpperCase()) || 'N/A'
    );
  };

  const getTransactionTypeColor = (type: string) => {
    switch (type?.toUpperCase()) {
      case 'PAYMENT':
        return 'green';
      case 'REFUND':
        return 'red';
      case 'DEPOSIT':
        return 'blue';
      case 'CHARGE_BACK':
        return 'orange';
      default:
        return 'gray';
    }
  };

  const handleLinkTransactions = async (onClose: any) => {
    try {
      if (!selectedTransactions.length) {
        throw new Error('Please select at least one transaction');
      }
      if (!params?.invoice_id && !invoice_id) {
        throw new Error('Invoice ID is required');
      }
      setLoading(true);
      const result = await linkInvoiceWithTransaction({
        invoice_id: Number(params?.invoice_id || invoice_id),
        transaction_ids: selectedTransactions,
      });

      const transactions: any[] = (clientTransactions as any)?.data || [];

      const statusData = {
        transactions: [
          ...(invoiceData?.data?.transactions || []),
          ...selectedTransactions.map((id) => {
            return transactions?.find((t: any) => t.id === id);
          }),
        ],
        total_price: invoiceData?.data?.total_price,
        discount: invoiceData?.data?.discount,
        tax_value: 0,
      };
      const invoiceStatus = determineInvoiceStatus(statusData);
      await updateInvoices({
        data: { status: invoiceStatus },
        id: invoiceData?.data?.id,
      });

      console.log('result is ', result);
      toaster.create({
        description: 'Transactions linked successfully',
        type: 'success',
      });
      await queryClient.invalidateQueries({
        queryKey: [
          queryKey.transactions.getByClientId,
          invoiceData?.data?.client_id,
        ],
      });
      await queryClient.invalidateQueries({
        queryKey: [
          'newsf-get-invoice-by-id',
          Number(params?.invoice_id || invoice_id),
        ],
      });

      await queryClient.invalidateQueries({
        queryKey: ['newsf-get-all-invoices'],
      });
      onClose();
    } catch (error: any) {
      console.log('error is ', error);
      toaster.create({
        description: error?.message,
        type: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteInvoice = async () => {
    try {
      if (!params?.invoice_id && !invoice_id) {
        throw new Error('Invoice ID is required');
      }

      const invoiceIdToDelete = Number(params?.invoice_id || invoice_id);

      await deleteInvoice(invoiceIdToDelete);

      // Invalidate relevant queries to refresh data.
      await queryClient.invalidateQueries({
        queryKey: [queryKey.newSf.getAllInvoices],
      });
      await queryClient.invalidateQueries({
        queryKey: [queryKey.newSf.getAllPurchases],
      });
      await queryClient.invalidateQueries({
        queryKey: [queryKey.invoices.getAllRaw],
      });
      await queryClient.invalidateQueries({
        queryKey: [queryKey.invoices.getByClients],
      });
      await queryClient.invalidateQueries({
        queryKey: [
          queryKey.transactions.getByClientId,
          invoiceData?.data?.client_id,
        ],
      });
      await queryClient.invalidateQueries({
        queryKey: [queryKey.client.getById, invoiceData?.data?.client_id],
      });

      // Navigate back or close modal after successful deletion
      // This will be handled by the component that calls this function
      router.back();
    } catch (error: any) {
      console.log('error is ', error);
      toaster.create({
        description: error?.message,
        type: 'error',
      });
    }
  };

  return {
    itemDisclosure,
    clientTransactions,
    invoiceData,
    getTransactionTypeColor,
    getPaymentMethodDisplay,
    formatCurrency,
    formatDate,
    handleTransactionSelect,
    handleSelectAll,
    selectedTransactions,
    handleLinkTransactions,
    linkTransactionLoading: loading,
    isFetching,
    handleDeleteInvoice,
    deleteLoading,
  };
};

export type TLinkTransactionHook = ReturnType<typeof useLinkInvoiceItemHook>;
