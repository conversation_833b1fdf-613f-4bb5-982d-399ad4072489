'use client';

// import { CustomModal } from '@/components/elements/modal/custom-modal';
import Status from '@/components/elements/status/Status';
import { Button } from '@/components/ui/button';
import { Box, Separator, Text, useDisclosure } from '@chakra-ui/react';
// import { Fragment } from 'react';
import { formatMoney } from '@/components/elements/format-money/FormatMoney';
import AnimateLoader from '@/components/elements/loader/animate-loader';
import { PDFGenerator } from '@/components/elements/pdf/PDF-Generator';
import { PDFViewer } from '@react-pdf/renderer';
import moment from 'moment';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { FiArrowLeft, FiFile } from 'react-icons/fi';
import SendInvoice from '../../slp/[id]/sessions/soapNoteFlow/SendInvoice';
import { useLinkInvoiceItemHook } from './_hook/useLinkInvoiceItemHook';
import Purchases from './Purchases';
import RecordPayment from './RecordPayment';
import {
  MenuContent,
  MenuItem,
  MenuRoot,
  MenuTrigger,
} from '@/components/ui/menu';
import { BsThreeDotsVertical } from 'react-icons/bs';
import { ConsentDialog } from '@/components/elements/dialog/ConsentDialog';
import { generateAndDownloadPDF } from '@/components/elements/pdf/Generate-PDF';
import { useMemo } from 'react';

const LinkTransaction = () => {
  const linkTransactionHook = useLinkInvoiceItemHook();
  const { invoiceData, isFetching, handleDeleteInvoice, deleteLoading } =
    linkTransactionHook;
  const searchParams = useSearchParams();
  const { transactions, total_price, discount } = invoiceData?.data ?? {};

  const router = useRouter();
  const deleteDisclosure = useDisclosure();
  console.log('invoiceData', invoiceData);

  // const isPackage = Boolean(package_used?.[0]);
  const totalPaid = transactions?.reduce(
    (prev: number, currentTransaction: any) => {
      return prev + Number(currentTransaction?.amount || 0);
    },
    0
  );

  let discountAmount = 0;
  if (discount && typeof discount === 'object') {
    if (discount.value > 0) {
      if (discount.type === 'percentage') {
        const subtotal = invoiceData?.data?.invoice_items?.reduce(
          (sum: number, item: any) => {
            const newPrice =
              Number(item?.quantity || 0) * Number(item?.price || 0);
            return sum + newPrice;
          },
          0
        );
        discountAmount = (Number(subtotal) * Number(discount.value)) / 100;
      } else {
        discountAmount = discount.value;
      }
    }
  } else if (typeof discount === 'number' && discount > 0) {
    discountAmount = discount;
  }

  const taxAmount = 0;
  const amountWithTax = Number(total_price) + Number(taxAmount);
  const amountWithDiscount = amountWithTax - discountAmount;
  console.log('amountWithDiscount>>>>>>', amountWithDiscount);
  // Calculate amount due: total_price - discount - totalPaid

  const amountDue = Number(total_price) - Number(totalPaid);
  const resolvedAmountDue = useMemo(() => {
    if (amountDue < 0) {
      return `(${formatMoney(Math.abs(amountDue), {
        currencyCode: invoiceData?.data?.currency_code,
      })})`;
    }
    return formatMoney(amountDue, {
      currencyCode: invoiceData?.data?.currency_code,
    });
  }, [amountDue, invoiceData?.data?.currency_code]);

  const organizationId = searchParams.get('organization_id');
  const userId = searchParams.get('user_id');
  let editInvoiceHref = `/invoices/edit/${invoiceData?.data?.id}`;

  if (organizationId) {
    editInvoiceHref += `?organization_id=${organizationId}&user_id=${userId}`;
  }

  const exportAsPDF = () => {
    generateAndDownloadPDF({
      name: String(
        invoiceData?.data?.client?.display_name || invoiceData?.data?.name
      ),
      email: String(
        invoiceData?.data?.client?.email || invoiceData?.data?.email
      ),
      receiptNumber: String(invoiceData?.data?.invoice_number),
      transactions: invoiceData?.data?.transactions ?? [],
      date: String(
        moment(invoiceData?.data?.invoice_date?.split('T')[0]).format(
          'MMMM D, YYYY'
        )
      ),
      dueDate: String(
        moment(
          invoiceData?.data?.due_date?.split('T')[0] ||
            invoiceData?.data?.invoice_date?.split('T')[0]
        ).format('MMMM D, YYYY')
      ),
      amountDue: amountDue,
      resolvedAmountDue,
      activity: String(invoiceData?.data?.product || ''),
      invoice: invoiceData?.data,
      referral: invoiceData?.data?.referral ?? '',
      quantity: Number(invoiceData?.data?.qty || 0),
      rate: Number(invoiceData?.data?.total_price || 0),
      balance: 0,
      memo: String(invoiceData?.data?.memo || ''),
    });
  };
  return (
    <Box maxW="1200px" mx="auto" p={6}>
      {/* Header */}
      <Box
        display={'flex'}
        gap={'1rem'}
        cursor={'pointer'}
        onClick={() => router.back()}
        alignItems={'center'}
      >
        <FiArrowLeft />
        <Text fontWeight={'bold'}>Go Back</Text>
      </Box>
      {isFetching ? (
        <AnimateLoader pt={'5rem'} />
      ) : !isFetching && invoiceData?.data ? (
        <Box>
          <Box my={'8px'}>
            <Text fontSize={'1.5rem'} fontWeight={'bold'}>
              Invoice #{invoiceData?.data?.invoice_number}
            </Text>
          </Box>
          <Separator my={'1rem'} borderColor={'#D3D3D3'} />
          <Box display={'flex'} justifyContent={'space-between'} mb={'1rem'}>
            <Box display={'flex'} gap={'1.5rem'}>
              <Box>
                <Text
                  fontSize={'sm'}
                  color={'GrayText'}
                  fontWeight={'semibold'}
                >
                  Status
                </Text>
                {/* <Box
                  px={'1rem'}
                  color={'gray.400'}
                  borderRadius={'sm'}
                  bg={'green.300'}
                  textAlign="center"
                > */}
                <Status
                  name={invoiceData?.data?.status}
                  // name={determineStatus()}
                />
                {/* </Box> */}
              </Box>
              <Box>
                <Text
                  fontSize={'sm'}
                  color={'GrayText'}
                  fontWeight={'semibold'}
                >
                  Customer
                </Text>
                <Text
                  onClick={() => {
                    router.replace(
                      `/contacts/${invoiceData?.data?.client?.id}?tab=profile`
                    );
                  }}
                  cursor={'pointer'}
                  color={'#E97A5B'}
                  fontWeight={'bold'}
                >
                  {invoiceData?.data?.client?.display_name}
                </Text>
              </Box>
            </Box>
            <Box display={'flex'} gap={'1.5rem'}>
              <Box>
                <Text
                  fontSize={'sm'}
                  color={'GrayText'}
                  fontWeight={'semibold'}
                >
                  Amount Due
                </Text>
                <Text>{resolvedAmountDue}</Text>
              </Box>
              <Box>
                <Text
                  fontSize={'sm'}
                  color={'GrayText'}
                  fontWeight={'semibold'}
                >
                  Due On
                </Text>
                <Text>
                  {moment(
                    invoiceData?.data?.due_date?.split('T')[0] ||
                      invoiceData?.data?.invoice_date?.split('T')[0]
                  ).format('MMMM D, YYYY')}
                </Text>
              </Box>
            </Box>
          </Box>

          <Box
            border={'1px solid #FEFEFE'}
            boxShadow={'lg'}
            rounded={'12px'}
            py={'1.5rem'}
            px={'10px'}
            w={'full'}
            minH={'6rem'}
            maxH={'fit-content'}
            scrollbar={'hidden'}
          >
            <Box
              display={'grid'}
              gridTemplateColumns={'1fr auto'}
              width={'full'}
              maxWidth={'full'}
              gap={'1.5rem'}
              overflow={'hidden'}
              justifyContent={'space-between'}
            >
              <Box display={'flex'} gap={'1.25rem'}>
                <Box
                  rounded={'full'}
                  fontSize={'18px'}
                  display={'flex'}
                  justifyContent={'center'}
                  alignItems={'center'}
                  minW={'36px'}
                  w={'36px'}
                  h={'36px'}
                  maxW={'36px'}
                  cursor={'pointer'}
                  color={'#E97A5B'}
                  border={'2px solid #E97A5B'}
                >
                  {' '}
                  <FiFile />
                </Box>
                <Box>
                  <Box
                    display={'flex'}
                    justifyContent={'normal'}
                    alignItems={'center'}
                    gap={'.5rem'}
                  >
                    <Text color={'GrayText'}>Create</Text>
                  </Box>

                  {/* {isEdit ? ( */}
                  <Text>
                    <b>Created: </b>
                    {'On ' +
                      moment(
                        invoiceData?.data?.invoice_date?.split('T')[0]
                      ).format('MMMM D, YYYY')}
                  </Text>
                  {/* ) : null} */}
                </Box>
              </Box>
              <Box display={'flex'} gap={'5px'} alignItems={'center'}>
                <Button
                  rounded={'2xl'}
                  fontSize={'14px'}
                  bg={'transparent'}
                  color={'#E97A5B'}
                  border={'2px solid #E97A5B'}
                  asChild
                >
                  <Link href={editInvoiceHref}>Edit invoice</Link>
                </Button>
                <Box>
                  <MenuRoot positioning={{ placement: 'bottom' }}>
                    <MenuTrigger
                      border={'none !important'}
                      boxShadow={'none !important'}
                      cursor={'pointer'}
                    >
                      <BsThreeDotsVertical />
                    </MenuTrigger>
                    <MenuContent cursor={'pointer'}>
                      <MenuItem
                        value="delete"
                        onClick={() => {
                          deleteDisclosure.onOpen();
                        }}
                        disabled={deleteLoading}
                      >
                        {deleteLoading ? 'Deleting...' : 'Delete Invoice'}
                      </MenuItem>
                      {/* export invoice pdf*/}
                      <MenuItem
                        value="export"
                        onClick={() => {
                          exportAsPDF();
                        }}
                        disabled={deleteLoading}
                      >
                        Export as PDF
                      </MenuItem>
                    </MenuContent>
                  </MenuRoot>
                  <ConsentDialog
                    handleSubmit={handleDeleteInvoice}
                    open={deleteDisclosure.open}
                    onOpenChange={deleteDisclosure.onClose}
                    heading={'Delete Invoice'}
                    note={
                      'Are you sure you want to delete this invoice? This action cannot be undone.'
                    }
                    isLoading={deleteLoading}
                  />
                </Box>
              </Box>
            </Box>
          </Box>

          <Box
            minH={'2rem'}
            bg={'gray.100'}
            // h={'100%'}
            w={'3px'}
            ml={'1.5rem'}
          ></Box>

          <SendInvoice
            soapNoteHook={undefined}
            booking={{
              slp_notes: {
                invoice: invoiceData?.data,
              },
            }}
          />

          {invoiceData?.data?.services_purchases?.length > 0 && (
            <>
              {' '}
              <Box
                minH={'2rem'}
                bg={'gray.100'}
                // h={'100%'}
                w={'3px'}
                ml={'1.5rem'}
              ></Box>
              <Purchases invoice={invoiceData?.data} />
            </>
          )}
          <Box
            minH={'2rem'}
            bg={'gray.100'}
            // h={'100%'}
            w={'3px'}
            ml={'1.5rem'}
          ></Box>

          <RecordPayment
            linkTransactionHook={linkTransactionHook}
            invoice={invoiceData?.data}
            transactions={transactions}
            amountDue={amountDue}
            resolvedAmountDue={resolvedAmountDue}
          />

          {/* invoice pdf */}

          {invoiceData?.data ? (
            <Box h={'100vh'} mt={'2rem'} shadow={'md'} overflow={'hidden'}>
              <PDFViewer
                style={{
                  height: '100%',
                  width: '100%',
                  border: 'none',
                }}
                showToolbar={true}
              >
                <PDFGenerator
                  name={String(
                    invoiceData?.data?.client?.display_name ||
                      invoiceData?.data?.name
                  )}
                  email={String(
                    invoiceData?.data?.client?.client_emails?.[0]?.email ||
                      invoiceData?.data?.email
                  )}
                  receiptNumber={String(invoiceData?.data?.invoice_number)}
                  transactions={invoiceData?.data?.transactions ?? []}
                  date={String(
                    moment(
                      invoiceData?.data?.invoice_date?.split('T')[0]
                    ).format('MMMM D, YYYY')
                  )}
                  dueDate={String(
                    moment(
                      invoiceData?.data?.due_date?.split('T')[0] ||
                        invoiceData?.data?.invoice_date?.split('T')[0]
                    ).format('MMMM D, YYYY')
                  )}
                  resolvedAmountDue={resolvedAmountDue}
                  amountDue={amountDue}
                  activity={String(invoiceData?.data?.product || '')}
                  invoice={invoiceData?.data}
                  referral={invoiceData?.data?.referral ?? ''}
                  quantity={Number(invoiceData?.data?.qty || 0)}
                  rate={Number(invoiceData?.data?.total_price || 0)}
                  balance={0}
                  memo={String(invoiceData?.data?.memo || '')}
                />
              </PDFViewer>
            </Box>
          ) : null}
        </Box>
      ) : null}
    </Box>
  );
};

export default LinkTransaction;
