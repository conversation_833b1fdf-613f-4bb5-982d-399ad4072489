import { toaster } from '@/components/ui/toaster';
import { useDisclosure } from '@chakra-ui/react';
import { useState } from 'react';

interface ContactStageFormData {
  id: string;
  label: string;
  isDefault?: boolean;
}

const initialFormData: ContactStageFormData = {
  id: '',
  label: '',
  isDefault: false,
};

const useContactStagesHook = ({
  contactStages,
  onUpdateContactStages,
}: {
  contactStages: ContactStageFormData[];
  onUpdateContactStages?: (stages: ContactStageFormData[]) => void;
}) => {
  const {
    open: isDeleteOpen,
    onClose: onDeleteClose,
    onOpen: onDeleteOpen,
  } = useDisclosure();

  const [formData, setFormData] =
    useState<ContactStageFormData>(initialFormData);
  const [errors, setErrors] = useState<Partial<ContactStageFormData>>({});
  const [isLoading, setIsLoading] = useState(false);

  const validateForm = (): boolean => {
    const newErrors: Partial<ContactStageFormData> = {};

    if (!formData.label.trim()) {
      newErrors.label = 'Stage name is required';
    }

    // Check for duplicate labels (excluding current item when editing)
    const isDuplicate = contactStages.some(
      (stage) =>
        stage.label.toLowerCase() === formData.label.toLowerCase() &&
        stage.id !== formData.id
    );

    if (isDuplicate) {
      newErrors.label = 'Stage name already exists';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (
    field: keyof ContactStageFormData | 'id',
    value: string | boolean
  ) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
    // Clear error when user starts typing
    if (errors[field as keyof ContactStageFormData]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    }
  };

  const handleSubmit = async (e?: React.FormEvent) => {
    e?.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setIsLoading(true);

      let updatedStages: ContactStageFormData[];

      if (
        formData.id &&
        contactStages.find((stage) => stage.id === formData.id)
      ) {
        // Update existing stage
        updatedStages = contactStages.map((stage) =>
          stage.id === formData.id
            ? { ...formData, label: formData.label.trim() }
            : stage
        );
        toaster.create({
          type: 'success',
          description: 'Contact stage updated successfully',
        });
      } else {
        // Add new stage
        const newStage: ContactStageFormData = {
          id: formData.label
            .toString()
            .trim()
            .toLowerCase()
            .replace(/' '/g, '_'),
          label: formData.label.trim(),
          isDefault: false,
        };
        updatedStages = [...contactStages, newStage];
        toaster.create({
          type: 'success',
          description: 'Contact stage created successfully',
        });
      }

      onUpdateContactStages?.(updatedStages);
      setFormData(initialFormData);
    } catch (error) {
      console.error('Error saving contact stage:', error);
      toaster.create({
        type: 'error',
        description: 'Failed to save contact stage',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (stageId: string) => {
    try {
      setIsLoading(true);

      const stageToDelete = contactStages.find((stage) => stage.id === stageId);

      if (stageToDelete?.isDefault) {
        toaster.create({
          type: 'error',
          description: 'Cannot delete default contact stages',
        });
        return;
      }

      const updatedStages = contactStages.filter(
        (stage) => stage.id !== stageId
      );
      onUpdateContactStages?.(updatedStages);

      setFormData(initialFormData);
      onDeleteClose();

      toaster.create({
        type: 'success',
        description: 'Contact stage deleted successfully',
      });
    } catch (error: any) {
      console.log('error', error);
      toaster.create({
        description: error?.message || 'Something went wrong.',
        type: 'error',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const openDeleteModal = (contactStage: any) => {
    setFormData(contactStage);
    onDeleteOpen();
  };
  return {
    formData,
    handleInputChange,
    handleSubmit,
    errors,
    isLoading,
    handleDelete,
    setFormData,
    isDeleteOpen,
    onDeleteClose,
    onDeleteOpen: openDeleteModal,
  };
};

export default useContactStagesHook;
