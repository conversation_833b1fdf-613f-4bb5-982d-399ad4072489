import { Colors } from '@/constants/colors';
import { Box, Center, Flex, Text } from '@chakra-ui/react';
import moment from 'moment';
import { FaCheck, FaExclamation } from 'react-icons/fa';

interface AnswerDetail {
  ans: string | number | string[] | null;
  id: number;
  qt: string;
  show_answers: string;
}

interface SubmittedAnswerProps {
  getFormsViewState?: any;
  item: {
    first_name: string;
    last_name: string;
    email: string;
    form_id: any;
    form_title: string;
    id: string | number;
    answer_details: AnswerDetail[];
    created_at: string;
    forms: {
      questions: any[];
    };
  };
}

const SubmittedAnswers = ({
  item,
  getFormsViewState,
}: SubmittedAnswerProps) => {
  const {
    first_name,
    last_name,
    answer_details,
    form_title,
    id,
    email,
    created_at,
    forms,
  } = item;

  // Adjust for the compulsory email question (subtract 1 from total questions)
  const adjustedTotalQuestions = Math.max(forms.questions.length - 1, 0);
  const isComplete = answer_details.length >= adjustedTotalQuestions;

  // Calculate the number of answered questions
  const numOfAnswered = answer_details.filter(
    (item: any) =>
      item.ans &&
      (typeof item.ans === 'string'
        ? item.ans.trim() !== ''
        : Array.isArray(item.ans)
          ? item.ans.length > 0
          : item.ans !== null)
  ).length;

  // Format the date using moment
  const formattedDate = moment(created_at).format('MMM DD, YYYY h:mm A');

  return (
    <Box
      borderRadius="md"
      width={'100%'}
      p={4}
      bg="white"
      cursor="pointer"
      _hover={{ backgroundColor: Colors.ORANGE.LIGHT }}
      boxShadow="md"
      transition="all 0.2s ease-in-out"
      mb={2}
      borderLeft={`4px solid ${isComplete ? Colors.GREEN.PRIMARY : Colors.ORANGE.PRIMARY}`}
    >
      <Box onClick={() => getFormsViewState('submitted-answers', id)}>
        <Flex justify="space-between" align="center">
          {/* Left Side */}
          <Flex align="center" gap={3}>
            <Center
              h={'40px'}
              w={'40px'}
              bg={isComplete ? Colors.GREEN.PRIMARY : Colors.ORANGE.PRIMARY}
              rounded={'full'}
            >
              {isComplete ? (
                <FaCheck size={15} color="white" />
              ) : (
                <FaExclamation size={15} color="white" />
              )}
            </Center>
            <Flex direction="column">
              <Text fontWeight="500">{form_title}</Text>
              <Text fontSize="sm" color="#7C7C7C">
                {first_name && last_name
                  ? `${first_name} ${last_name}${email ? ` - ${email}` : ''}`
                  : email}
              </Text>
              <Text fontSize="xs" color="#7C7C7C" mt={1}>
                Submitted: {formattedDate}
                {!isComplete && (
                  <Text as="span" color={Colors.ORANGE.PRIMARY} ml={2}>
                    (Incomplete)
                  </Text>
                )}
              </Text>
            </Flex>
          </Flex>

          {/* Right Side */}
          <Box textAlign="right" fontSize="12px">
            <Text fontWeight="500">
              {adjustedTotalQuestions} question
              {adjustedTotalQuestions !== 1 ? 's' : ''}
            </Text>
            <Text
              color={isComplete ? Colors.GREEN.PRIMARY : Colors.ORANGE.PRIMARY}
            >
              {numOfAnswered}/{adjustedTotalQuestions} answered
              {!isComplete && (
                <Text as="span" ml={1}>
                  ❗
                </Text>
              )}
            </Text>
          </Box>
        </Flex>
      </Box>
    </Box>
  );
};

export default SubmittedAnswers;
